"""
Utility functions for AI CLI
"""

import re
from typing import Optional
from rich.console import Console
from rich.syntax import Syntax
from rich.panel import Panel

try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
except ImportError:
    TIKTOKEN_AVAILABLE = False


def detect_code_language(code: str) -> Optional[str]:
    """
    Detect programming language from code snippet

    Args:
        code: Code snippet to analyze

    Returns:
        Detected language or None
    """
    # Simple language detection based on common patterns
    patterns = {
        'python': [
            r'def\s+\w+\s*\(',
            r'import\s+\w+',
            r'from\s+\w+\s+import',
            r'if\s+__name__\s*==\s*["\']__main__["\']',
            r'print\s*\(',
        ],
        'javascript': [
            r'function\s+\w+\s*\(',
            r'const\s+\w+\s*=',
            r'let\s+\w+\s*=',
            r'var\s+\w+\s*=',
            r'console\.log\s*\(',
            r'=>',
        ],
        'java': [
            r'public\s+class\s+\w+',
            r'public\s+static\s+void\s+main',
            r'System\.out\.println',
            r'import\s+java\.',
        ],
        'cpp': [
            r'#include\s*<\w+>',
            r'int\s+main\s*\(',
            r'std::',
            r'cout\s*<<',
            r'cin\s*>>',
        ],
        'c': [
            r'#include\s*<\w+\.h>',
            r'int\s+main\s*\(',
            r'printf\s*\(',
            r'scanf\s*\(',
        ],
        'bash': [
            r'#!/bin/bash',
            r'#!/bin/sh',
            r'\$\w+',
            r'echo\s+',
        ],
        'sql': [
            r'SELECT\s+',
            r'FROM\s+\w+',
            r'WHERE\s+',
            r'INSERT\s+INTO',
            r'UPDATE\s+\w+\s+SET',
        ],
        'html': [
            r'<html>',
            r'<head>',
            r'<body>',
            r'<div\s*.*?>',
        ],
        'css': [
            r'\w+\s*\{[^}]*\}',
            r'@media\s+',
            r'#\w+\s*\{',
            r'\.\w+\s*\{',
        ],
        'json': [
            r'^\s*\{',
            r'^\s*\[',
            r'"\w+"\s*:',
        ],
        'yaml': [
            r'^\w+\s*:',
            r'^\s*-\s+\w+',
        ],
    }

    # Count matches for each language
    scores = {}
    for lang, lang_patterns in patterns.items():
        score = 0
        for pattern in lang_patterns:
            if re.search(pattern, code, re.MULTILINE | re.IGNORECASE):
                score += 1
        if score > 0:
            scores[lang] = score

    # Return language with highest score
    if scores:
        return max(scores, key=scores.get)

    return None


def format_code_block(code: str, language: Optional[str] = None) -> Panel:
    """
    Format code block with syntax highlighting

    Args:
        code: Code to format
        language: Programming language (auto-detected if None)

    Returns:
        Rich Panel with formatted code
    """
    if language is None:
        language = detect_code_language(code)

    if language:
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        return Panel(syntax, title=f"Code ({language})", border_style="green")
    else:
        return Panel(code, title="Code", border_style="yellow")


def extract_code_blocks(text: str) -> list:
    """
    Extract code blocks from markdown-style text

    Args:
        text: Text containing potential code blocks

    Returns:
        List of tuples (code, language)
    """
    # Pattern for fenced code blocks
    pattern = r'```(\w+)?\n(.*?)\n```'
    matches = re.findall(pattern, text, re.DOTALL)

    code_blocks = []
    for lang, code in matches:
        code_blocks.append((code.strip(), lang if lang else None))

    return code_blocks


def is_multiline_input(text: str) -> bool:
    """
    Check if input appears to be multiline code or text

    Args:
        text: Input text to check

    Returns:
        True if appears to be multiline input
    """
    # Check for common multiline indicators
    multiline_indicators = [
        r'def\s+\w+\s*\(',  # Python function
        r'class\s+\w+',     # Class definition
        r'if\s+.*:$',       # If statement
        r'for\s+.*:$',      # For loop
        r'while\s+.*:$',    # While loop
        r'try\s*:$',        # Try block
        r'with\s+.*:$',     # With statement
        r'\{$',             # Opening brace
        r'function\s+\w+',  # JavaScript function
    ]

    for pattern in multiline_indicators:
        if re.search(pattern, text, re.MULTILINE):
            return True

    return False


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    Truncate text to specified length with ellipsis

    Args:
        text: Text to truncate
        max_length: Maximum length

    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."


def count_tokens(text: str, model: str = "gpt-3.5-turbo") -> int:
    """
    Count tokens in text using tiktoken

    Args:
        text: Text to count tokens for
        model: Model name for encoding (defaults to gpt-3.5-turbo)

    Returns:
        Number of tokens, or improved estimation if tiktoken unavailable
    """
    if not TIKTOKEN_AVAILABLE:
        # Improved fallback: word-based estimation (more accurate than char/4)
        words = len(text.split()) if text.strip() else 0
        # Average tokens per word is approximately 1.3 for most languages
        # Add some tokens for punctuation and special characters
        punctuation_tokens = len(re.findall(r'[^\w\s]', text))
        return max(1, int(words * 1.3 + punctuation_tokens * 0.5))

    try:
        # Try to get encoding for the specific model
        if "qwen" in model.lower():
            # For Qwen models, use cl100k_base encoding (similar to GPT-4)
            encoding = tiktoken.get_encoding("cl100k_base")
        elif "gpt-4" in model.lower():
            encoding = tiktoken.encoding_for_model("gpt-4")
        elif "gpt-3.5" in model.lower():
            encoding = tiktoken.encoding_for_model("gpt-3.5-turbo")
        else:
            # Default to cl100k_base for unknown models
            encoding = tiktoken.get_encoding("cl100k_base")

        return len(encoding.encode(text))
    except Exception:
        # Improved fallback if encoding fails
        words = len(text.split()) if text.strip() else 0
        punctuation_tokens = len(re.findall(r'[^\w\s]', text))
        return max(1, int(words * 1.3 + punctuation_tokens * 0.5))


def format_token_count(token_count: int) -> str:
    """
    Format token count for display

    Args:
        token_count: Number of tokens

    Returns:
        Formatted token count string
    """
    if token_count < 1000:
        return f"{token_count} tokens"
    elif token_count < 1000000:
        return f"{token_count/1000:.1f}k tokens"
    else:
        return f"{token_count/1000000:.1f}M tokens"


def format_token_count_with_cost(token_count: int, model: str = "qwen/qwen-2.5-coder-32b-instruct:free", demo_mode: bool = False) -> str:
    """
    Format token count with estimated cost for display

    Args:
        token_count: Number of tokens
        model: Model name for cost calculation
        demo_mode: Whether to show demo pricing

    Returns:
        Formatted token count string with cost
    """
    # Enhanced mock pricing for demo mode (these are not real prices)
    mock_pricing = {
        "qwen/qwen-2.5-coder-32b-instruct:free": 0.0,  # Free model
        "qwen/qwen-2.5-coder-32b-instruct": 0.0005,  # Mock pricing
        "gpt-4": 0.03,  # $0.03 per 1K tokens (mock)
        "gpt-4-turbo": 0.01,  # Mock pricing
        "gpt-3.5-turbo": 0.002,  # $0.002 per 1K tokens (mock)
        "claude-3-opus": 0.015,  # Mock pricing
        "claude-3-sonnet": 0.003,  # Mock pricing
        "claude-3-haiku": 0.00025,  # Mock pricing
        "gemini-pro": 0.001,  # Mock pricing
    }

    # Get base pricing (per 1K tokens)
    base_cost = mock_pricing.get(model, 0.001)  # Default mock cost

    # Calculate cost
    cost = (token_count / 1000) * base_cost

    token_str = format_token_count(token_count)

    if demo_mode:
        if cost == 0:
            return f"{token_str} (Demo - Free)"
        elif cost < 0.01:
            return f"{token_str} (Demo - ~$0.00)"
        else:
            return f"{token_str} (Demo - ~${cost:.3f})"
    else:
        if cost == 0:
            return f"{token_str} (Free)"
        elif cost < 0.01:
            return f"{token_str} (~$0.00)"
        else:
            return f"{token_str} (~${cost:.3f})"


def get_session_cost_summary(conversation_history: list, model: str, demo_mode: bool = False) -> dict:
    """
    Calculate session cost summary for display

    Args:
        conversation_history: List of conversation messages
        model: Model name for cost calculation
        demo_mode: Whether to show demo pricing

    Returns:
        Dictionary with session cost information
    """
    total_tokens = 0
    user_tokens = 0
    ai_tokens = 0

    for message in conversation_history:
        tokens = count_tokens(message.content if hasattr(message, 'content') else str(message), model)
        total_tokens += tokens

        if hasattr(message, 'role'):
            if message.role == 'user':
                user_tokens += tokens
            else:
                ai_tokens += tokens

    # Calculate costs using the enhanced pricing
    mock_pricing = {
        "qwen/qwen-2.5-coder-32b-instruct:free": 0.0,
        "qwen/qwen-2.5-coder-32b-instruct": 0.0005,
        "gpt-4": 0.03,
        "gpt-4-turbo": 0.01,
        "gpt-3.5-turbo": 0.002,
        "claude-3-opus": 0.015,
        "claude-3-sonnet": 0.003,
        "claude-3-haiku": 0.00025,
        "gemini-pro": 0.001,
    }

    base_cost = mock_pricing.get(model, 0.001)
    total_cost = (total_tokens / 1000) * base_cost

    return {
        "total_tokens": total_tokens,
        "user_tokens": user_tokens,
        "ai_tokens": ai_tokens,
        "total_cost": total_cost,
        "formatted_cost": f"${total_cost:.4f}" if total_cost > 0 else "Free",
        "demo_mode": demo_mode,
        "model": model
    }


def get_real_time_token_count(text: str, model: str = "qwen/qwen-2.5-coder-32b-instruct:free", demo_mode: bool = False) -> dict:
    """
    Get real-time token count with additional metrics

    Args:
        text: Text to analyze
        model: Model name for token counting
        demo_mode: Whether to show demo pricing (ignored for clean display)

    Returns:
        Dictionary with token count, character count, and word count
    """
    token_count = count_tokens(text, model)
    char_count = len(text)
    word_count = len(text.split()) if text.strip() else 0

    return {
        "tokens": token_count,
        "characters": char_count,
        "words": word_count,
        "formatted": format_token_count(token_count)  # Use clean format without cost info
    }